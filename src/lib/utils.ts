import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { ApiError } from "@/api";
import { type VisibilityState, type ColumnSizingState, type ColumnOrderState } from "@tanstack/react-table";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function isError(data: unknown): data is ApiError {
  return typeof data === "object" && data !== null && "error" in data && typeof (data as ApiError).error === "string";
}

export type CachedTableState = {
  columnVisibility: VisibilityState;
  columnSizing: ColumnSizingState;
  columnOrder: ColumnOrderState;
  rowsPerPage: number;
};

const getTableKey = (tableKey: string) => `${tableKey}`;
const getEmtyState: () => CachedTableState = () => {
  return {
    columnOrder: [],
    columnSizing: {},
    columnVisibility: {},
    rowsPerPage: 10,
  };
};

// Save functions
export function saveColumnOrder(tableKey: string, columnOrder: ColumnOrderState) {
  const key = getTableKey(tableKey);
  const state = getTableState(tableKey) || {};
  state.columnOrder = columnOrder;
  localStorage.setItem(key, JSON.stringify(state));
}

export function saveColumnSizing(tableKey: string, columnSizing: ColumnSizingState) {
  const key = getTableKey(tableKey);
  const state = getTableState(tableKey) || {};
  state.columnSizing = columnSizing;
  localStorage.setItem(key, JSON.stringify(state));
}

export function saveColumnVisibility(tableKey: string, columnVisibility: VisibilityState) {
  const key = getTableKey(tableKey);
  const state = getTableState(tableKey) || {};
  state.columnVisibility = columnVisibility;
  localStorage.setItem(key, JSON.stringify(state));
}

export function saveRowsPerPage(tableKey: string, rowsPerPage: number) {
  const key = getTableKey(tableKey);
  const state = getTableState(tableKey) || {};
  state.rowsPerPage = rowsPerPage;
  localStorage.setItem(key, JSON.stringify(state));
}

// Get full table state
function getTableState(tableKey: string): CachedTableState {
  const key = getTableKey(tableKey);
  const raw = localStorage.getItem(key);
  if (!raw) return getEmtyState();
  try {
    return JSON.parse(raw) as CachedTableState;
  } catch {
    return getEmtyState();
  }
}

// Get functions
export function getColumnOrder(tableKey: string): ColumnOrderState {
  return getTableState(tableKey).columnOrder;
}

export function getColumnSizing(tableKey: string): ColumnSizingState {
  return getTableState(tableKey).columnSizing;
}

export function getColumnVisibility(tableKey: string): VisibilityState {
  return getTableState(tableKey).columnVisibility;
}

export function getRowsPerPage(tableKey: string): number {
  return getTableState(tableKey).rowsPerPage;
}
