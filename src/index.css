@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.5rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823); /* primary-black */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823); /* primary-black */
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823); /* primary-black */
  --primary: oklch(0.4 0.18 264); /* primary-blue #124191 */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.93 0.02 230); /* gray1 #edf2f5 */
  --secondary-foreground: oklch(0.3 0.03 260); /* gray5 #273142 */
  --muted: oklch(0.93 0.02 230); /* gray1 #edf2f5 */
  --muted-foreground: oklch(0.65 0.03 250); /* gray3 #98a2ae */
  --accent: oklch(0.93 0.02 230); /* accent-blue #00c9ff */
  --accent-foreground: oklch(0.141 0.005 285.823); /* primary-black */
  --destructive: oklch(0.65 0.3 15); /* accent-red #ff3154 */
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.85 0.03 240); /* gray2 #bec8d2 */
  --input: oklch(0.93 0.02 230); /* gray1 #edf2f5 */
  --ring: oklch(0.4 0.18 264); /* primary-blue #124191 */
  --chart-1: oklch(0.4 0.18 264); /* primary-blue */
  --chart-2: oklch(0.8 0.2 200); /* accent-blue */
  --chart-3: oklch(0.65 0.3 15); /* accent-red */
  --chart-4: oklch(0.9 0.25 110); /* accent-yellow */
  --chart-5: oklch(0.7 0.25 140); /* accent-green */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823); /* primary-black */
  --sidebar-primary: oklch(0.4 0.18 264); /* primary-blue */
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.93 0.02 230); /* gray1 */
  --sidebar-accent-foreground: oklch(0.3 0.03 260); /* gray5 */
  --sidebar-border: oklch(0.85 0.03 240); /* gray2 */
  --sidebar-ring: oklch(0.4 0.18 264); /* primary-blue */
}

.dark {
  --background: oklch(0.141 0.005 285.823); /* primary-black #001135 */
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.3 0.03 260); /* gray5 #273142 */
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.3 0.03 260); /* gray5 #273142 */
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.4 0.18 264); /* primary-blue #124191 */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.3 0.03 260); /* gray5 #273142 */
  --secondary-foreground: oklch(0.93 0.02 230); /* gray1 #edf2f5 */
  --muted: oklch(0.25 0.03 260); /* darker gray5 */
  --muted-foreground: oklch(0.65 0.03 250); /* gray3 #98a2ae */
  --accent: oklch(0.93 0.02 230); /* accent-blue #00c9ff */
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.65 0.3 15); /* accent-red #ff3154 */
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.45 0.03 255); /* gray4 #4d5766 */
  --input: oklch(0.3 0.03 260); /* gray5 #273142 */
  --ring: oklch(0.4 0.18 264); /* primary-blue #124191 */
  --chart-1: oklch(0.4 0.18 264); /* primary-blue */
  --chart-2: oklch(0.8 0.2 200); /* accent-blue */
  --chart-3: oklch(0.65 0.3 15); /* accent-red */
  --chart-4: oklch(0.9 0.25 110); /* accent-yellow */
  --chart-5: oklch(0.7 0.25 140); /* accent-green */
  --sidebar: oklch(0.3 0.03 260); /* gray5 #273142 */
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.4 0.18 264); /* primary-blue */
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.3 0.03 260); /* gray5 */
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.4 0.18 264); /* primary-blue */
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }
}

@layer base {
  button,
  [role="button"] {
    cursor: pointer;
  }
}

/* Table styles for resizable columns */
.resizable-table {
  table-layout: fixed;
  width: 100%;
}

/* Make sure content doesn't overflow */
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* Style for when a column is being resized */
.resizing {
  background: rgba(0, 0, 0, 0.1);
  user-select: none;
}

/* Add a subtle visual indicator for the resize handle */
.cursor-col-resize {
  cursor: col-resize;
}

.cursor-col-resize:hover {
  background: rgba(0, 0, 0, 0.05);
}
