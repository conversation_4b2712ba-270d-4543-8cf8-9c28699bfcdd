"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ChevronDown } from "lucide-react";
import type { Table } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ColumnOrderDropdownProps<T> {
  table: Table<T>;
}

export function ColumnOrderDropdown<T>({ table }: ColumnOrderDropdownProps<T>) {
  // Get current column order
  const currentOrder = table.getState().columnOrder.length
    ? [...table.getState().columnOrder]
    : table.getAllLeafColumns().map((d) => d.id);

  // Move column up in order
  const moveColumnUp = (columnId: string) => {
    const currentIndex = currentOrder.findIndex((id) => id === columnId);
    if (currentIndex <= 0) return; // Already at the top or not found

    const newOrder = [...currentOrder];
    const temp = newOrder[currentIndex];
    newOrder[currentIndex] = newOrder[currentIndex - 1];
    newOrder[currentIndex - 1] = temp;

    table.setColumnOrder(newOrder);
  };

  // Move column down in order
  const moveColumnDown = (columnId: string) => {
    const currentIndex = currentOrder.findIndex((id) => id === columnId);
    if (currentIndex === -1 || currentIndex >= currentOrder.length - 1) return; // Not found or already at the bottom

    const newOrder = [...currentOrder];
    const temp = newOrder[currentIndex];
    newOrder[currentIndex] = newOrder[currentIndex + 1];
    newOrder[currentIndex + 1] = temp;

    table.setColumnOrder(newOrder);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 cursor-pointer">
          Column Order
          <ChevronDown className="h-3.5 w-3.5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px]">
        <DropdownMenuLabel>Reorder Columns</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {table
          .getAllLeafColumns()
          .filter((column) => column.id !== "select" && column.getCanHide())
          .map((column) => {
            const columnIndex = currentOrder.findIndex((id) => id === column.id);
            const isFirst = columnIndex === 0 || columnIndex === 1; // Account for select column
            const isLast = columnIndex === currentOrder.length - 1;

            return (
              <DropdownMenuItem key={column.id} className="flex justify-between items-center">
                <span className="capitalize">{column.id}</span>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={(e) => {
                      e.stopPropagation();
                      moveColumnUp(column.id);
                    }}
                    disabled={isFirst}
                  >
                    <ArrowUp className="h-3.5 w-3.5" />
                    <span className="sr-only">Move {column.id} up</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={(e) => {
                      e.stopPropagation();
                      moveColumnDown(column.id);
                    }}
                    disabled={isLast}
                  >
                    <ArrowDown className="h-3.5 w-3.5" />
                    <span className="sr-only">Move {column.id} down</span>
                  </Button>
                </div>
              </DropdownMenuItem>
            );
          })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
