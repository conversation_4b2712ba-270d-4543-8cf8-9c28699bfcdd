import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ReactNode } from "react";
import { Link } from "@tanstack/react-router";

type OptionsCardProp = {
  title: string;
  description: string;
  links: {
    link_name?: string;
    linkComponent?: ReactNode | null;
  }[];
};

export default function OptionsCard({ title, description, links }: OptionsCardProp) {
  return (
    <div className="h-full">
      <Card className="p-0 h-full flex flex-col">
        <CardHeader className="pb-2 pt-4 bg-primary text-white rounded-t-lg">
          <CardTitle>{title}</CardTitle>
          <CardDescription className="text-gray-100">{description}</CardDescription>
        </CardHeader>
        <CardContent className="grow pb-6 flex flex-col justify-between">
          <ul className="space-y-2 mb-5">
            {links.map((link) => {
              return (
                <li>
                  {link.linkComponent ? (
                    link.linkComponent
                  ) : (
                    <Link to="/" className="h-auto p-0 cursor-pointer" disabled={true}>
                      {link.link_name}
                      <span className="ml-2 inline-flex h-2 w-2 rounded-full bg-amber-400"></span>
                    </Link>
                  )}
                </li>
              );
            })}
          </ul>
          <div className="pt-3 border-t border-gray-200">
            <div className="w-full flex items-center gap-2 text-xs text-gray-500">
              <span className="inline-flex h-2 w-2 rounded-full bg-amber-400"></span>
              <span>Feature not in development</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
