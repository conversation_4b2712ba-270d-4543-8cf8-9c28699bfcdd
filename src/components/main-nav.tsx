import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "@tanstack/react-router";
import UserContext from "@/context/UserContext";
import { useContext } from "react";

export function MainNav() {
  const userCtx = useContext(UserContext);
  return (
    <nav className="flex items-center space-x-4 lg:space-x-6">
      <Button variant="ghost" asChild>
        <Link to="/">Home</Link>
      </Button>
      <Button variant="ghost" asChild>
        <Link to="/search-menu">Search</Link>
      </Button>
      <Button variant="ghost" asChild>
        <Link
          to="/my-errors"
          search={{
            ownerTest: userCtx?.owner,
          }}
        >
          {" "}
          My Errors
        </Link>
      </Button>
    </nav>
  );
}
