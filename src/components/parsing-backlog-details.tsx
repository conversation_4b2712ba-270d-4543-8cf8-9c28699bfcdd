"use client";

import { useState } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BacklogItem } from "./layout";

interface ParsingBacklogDetailsProps {
  backlogCount: number;
  backlogItems: BacklogItem[];
}

export function ParsingBacklogDetails({ backlogCount, backlogItems }: ParsingBacklogDetailsProps) {
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 gap-1 border-dashed cursor-pointer flex items-center">
          <span>Parsing backLog: {backlogCount}</span>
          <ChevronDown className="h-2 w-3.5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <Card className="border-0 shadow-none py-0">
          <CardHeader className="px-0 pt-0">
            <CardTitle className="text-sm font-medium">Parsing Backlog Details</CardTitle>
          </CardHeader>
          <CardContent className="px-0 pb-0">
            <div>
              <div className="space-y-3">
                {backlogItems.map((item) => (
                  <div key={item.id} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Backlog #{item.id}</span>
                      <span>{item.duration}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
}
