import { Header } from "@tanstack/react-table";
import { TestCase } from "./table/testcase-table";

export const ColumnResizer = ({ header }: { header: Header<TestCase, unknown> }) => {
  if (header.column.getCanResize() === false) return <></>;

  return (
    <div
      {...{
        onMouseDown: header.getResizeHandler(),
        onTouchStart: header.getResizeHandler(),
        className: `absolute top-0 right-0 cursor-col-resize w-3 h-full hover:bg-gray-300 hover:w-2`,
        style: {
          userSelect: "none",
          touchAction: "none",
        },
      }}
    />
  );
};
