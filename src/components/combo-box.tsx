"use client";

import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useQuery, useQueryClient } from "@tanstack/react-query";

export type ComboboxOption = {
  id: string;
  text: string;
};

export type ComboboxGroup = {
  text: string;
  children?: ComboboxOption[];
  id?: string;
};

interface ComboboxProps {
  placeholder: string;
  emptyMessage?: string;
  value?: string;
  onChange: (value: string) => void;
  className?: string;
  queryHook: (searchTerm: string) => ReturnType<typeof useQuery>;
  type: string;
}

export function Combobox({
  placeholder,
  emptyMessage = "No results found.",
  value,
  onChange,
  className,
  queryHook,
  type,
}: ComboboxProps) {
  useQueryClient();
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const { data } = queryHook(searchQuery);

  // Find the selected option's label
  const selectedLabel = React.useMemo(() => {
    if (!value || !data) return "";

    if (type === "string") {
      // Handle simple string array format
      const stringGroups = data as string[];
      return stringGroups.find((item) => item === value) || "";
    } else {
      // Handle ComboboxGroup array format
      const comboBoxGroups = data as ComboboxGroup[];
      for (const group of comboBoxGroups) {
        if (group.children) {
          const option = group.children.find((child) => child.id === value);
          if (option) return option.text;
        }
      }
    }

    return value;
  }, [value, data]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between ", className)}
        >
          {value ? selectedLabel : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={`Search ${placeholder.toLowerCase()}...`}
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandEmpty>{emptyMessage}</CommandEmpty>
          <CommandList>
            {data ? (
              type === "string" ? (
                (data as string[]).map((group) => (
                  <CommandItem
                    key={group}
                    value={group}
                    onSelect={(currentValue) => {
                      onChange(currentValue === value ? "" : currentValue);
                      setOpen(false);
                    }}
                  >
                    <Check className={cn("mr-2 h-4 w-4", value === group ? "opacity-100" : "opacity-0")} />
                    {group}
                  </CommandItem>
                ))
              ) : (
                (data as ComboboxGroup[]).map((group) => {
                  if (group.id) {
                    return (
                      <CommandItem
                        key={group.text}
                        value={group.text}
                        onSelect={(currentValue) => {
                          onChange(currentValue === value ? "" : currentValue);
                          setOpen(false);
                        }}
                      >
                        <Check className={cn("mr-2 h-4 w-4", value === group.text ? "opacity-100" : "opacity-0")} />
                        {group.text}
                      </CommandItem>
                    );
                  }
                  return (
                    <CommandGroup key={group.text} heading={group.text}>
                      {group.children &&
                        group.children.map((option) => (
                          <CommandItem
                            key={option.id}
                            value={option.id}
                            onSelect={(currentValue) => {
                              onChange(currentValue === value ? "" : currentValue);
                              setOpen(false);
                            }}
                          >
                            <Check className={cn("mr-2 h-4 w-4", value === option.id ? "opacity-100" : "opacity-0")} />
                            {option.text}
                          </CommandItem>
                        ))}
                    </CommandGroup>
                  );
                })
              )
            ) : (
              <></>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
