"use client";

import { useState, useEffect } from "react";
import type { Column } from "@tanstack/react-table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Option } from "./search-form";

interface ColumnFilterDropdownProps<T> {
  column: Column<T, unknown>;
  options: Option[];
  placeholder?: string;
}

export function ColumnFilterDropdown<T>({ column, options, placeholder = "Filter..." }: ColumnFilterDropdownProps<T>) {
  const [value, setValue] = useState<string>("");

  // Sync with column filter value
  useEffect(() => {
    const currentFilterValue = column.getFilterValue() as string;
    if (currentFilterValue !== value) {
      setValue(currentFilterValue || "");
    }
  }, [column, value]);

  const handleValueChange = (newValue: string) => {
    setValue(newValue);
    if (newValue === "all") {
      newValue = "";
    }
    column.setFilterValue(newValue || undefined);
  };

  return (
    <Select value={value} onValueChange={handleValueChange}>
      <SelectTrigger className="h-7">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All</SelectItem>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
