import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "../ui/checkbox";
import { TestCase } from "./testcase-table";
import { Button } from "../ui/button";
import { ArrowUpDown, ExternalLink } from "lucide-react";
import { Badge } from "../ui/badge";
import { numericFilterFn, statusFilterFn } from "./filters";
import { status_colors, formatDuration } from "./formatters";
import { Link } from "@tanstack/react-router";

export type TableView = {
  key: string;
  columns: ColumnDef<TestCase>[];
};

export const STANDARD_TEST_TABLE_VIEW: TableView = {
  key: "STANDARD_TEST_TABLE",
  columns: [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableHiding: false,
      enableSorting: false,
      enableResizing: false,
      size: 40,
      minSize: 40,
      maxSize: 40,
    },
    {
      id: "branch",
      accessorKey: "branch",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            branch
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="truncate">{row.getValue("branch")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "suite",
      accessorKey: "suite",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            suite
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => (
        <div className="truncate">
          <span title={row.getValue("suite") || ""}>{row.getValue("suite") || "-"}</span>
        </div>
      ),
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "testCase",
      accessorKey: "testCase",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            testCase
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => (
        <div className="font-medium text-primary truncate">
          {" "}
          <span title={row.getValue("testCase") || ""}>
            {" "}
            <Link
              to="/test-details"
              search={{
                testCase: row.getValue("testCase") as string,
                branch: row.original.branch,
                branch_id: row.original.branch_id,
                tasId: row.original.tasId,
              }}
            >
              {row.getValue("testCase") || "-"}
            </Link>
          </span>
        </div>
      ),
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "dur",
      accessorKey: "dur",
      header: "dur",
      cell: ({ row }) => <div className="truncate">{formatDuration(row.getValue("dur"))}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "lastChange",
      accessorKey: "lastChange",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            lastChange
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="truncate">{row.getValue("lastChange") || "-"}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "status",
      accessorKey: "status",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        let status = row.getValue("status") as string;
        return (
          <Badge
            variant={status === "GHOST" ? "outline" : "default"}
            className={
              status in status_colors
                ? status_colors[status as string]
                : status.startsWith("ROTTEN")
                  ? "bg-yellow-800"
                  : "bg-blue-200"
            }
          >
            {status}
          </Badge>
        );
      },
      enableResizing: true,
      minSize: 50,
      filterFn: statusFilterFn,
    },
    {
      id: "lastP",
      accessorKey: "lastP",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            lastP
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const lastP = row.getValue("lastP") as string;
        const lastPLink = (row.original as TestCase).lastPassedLink;

        if (!lastP) return <div className="truncate">-</div>;

        if (lastPLink) {
          return (
            <a
              href={lastPLink}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline flex items-center"
            >
              <span className="truncate">{lastP}</span>
              <ExternalLink className="ml-1 h-3 w-3" />
            </a>
          );
        }

        return <div className="truncate">{lastP}</div>;
      },
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "lastF",
      accessorKey: "lastF",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            lastF
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const lastF = row.getValue("lastF") as string;
        const lastFLink = (row.original as TestCase).lastFailedLink;

        if (!lastF) return <div className="truncate">-</div>;

        if (lastFLink) {
          return (
            <a
              href={lastFLink}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline flex items-center"
            >
              <span className="truncate">{lastF}</span>
              <ExternalLink className="ml-1 h-3 w-3" />
            </a>
          );
        }

        return <div className="truncate">{lastF}</div>;
      },
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "lastTopo",
      accessorKey: "lastTopo",
      header: "lastTopo",
      cell: ({ row }) => (
        <div className="font-medium text-primary truncate">
          {" "}
          <span title={row.getValue("lastTopo") || ""}>{row.getValue("lastTopo") || "-"}</span>
        </div>
      ),
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "owner",
      accessorKey: "owner",
      header: "owner",
      cell: ({ row }) => <div className="text-primary truncate">{row.getValue("owner")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "delegate",
      accessorKey: "delegate",
      header: "delegate",
      cell: ({ row }) => <div className="truncate">{row.getValue("delegate") || "-"}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "reason",
      accessorKey: "reason",
      header: "reason",
      cell: ({ row }) => <div className="truncate">{row.getValue("reason") || "-"}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "action",
      accessorKey: "action",
      header: "action",
      cell: ({ row }) => <div className="truncate">{row.getValue("action") || "-"}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "passC",
      accessorKey: "passC",
      header: "passC",
      cell: ({ row }) => <div className="truncate">{row.getValue("passC")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "failC",
      accessorKey: "failC",
      header: "failC",
      cell: ({ row }) => <div className="truncate">{row.getValue("failC")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "regLevel",
      accessorKey: "regLevel",
      header: "regLevel",
      cell: ({ row }) => {
        const regLevel = row.getValue("regLevel") as string;
        return (
          <Badge
            variant="outline"
            className={regLevel === "extensive" ? "bg-purple-100 text-purple-800 hover:bg-purple-100" : ""}
          >
            {regLevel}
          </Badge>
        );
      },
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "errorString",
      accessorKey: "errorString",
      header: "errorString",
      cell: ({ row }) => (
        <div className="truncate">
          <span title={row.getValue("errorString") || ""}>{row.getValue("errorString") || "-"}</span>
        </div>
      ),
      enableResizing: true,
      minSize: 50,
    },
  ],
};

export const RESULTS_PER_TOPO_TABLE: TableView = {
  key: "RESULTS_PER_TOPO_TABLE",
  columns: [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableHiding: false,
      enableSorting: false,
      enableResizing: false,
      size: 40,
      minSize: 40,
      maxSize: 40,
    },
    {
      id: "branch",
      accessorKey: "branch",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            branch
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="truncate">{row.getValue("branch")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "suite",
      accessorKey: "suite",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            suite
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => (
        <div className="truncate">
          <span title={row.getValue("suite") || ""}>{row.getValue("suite") || "-"}</span>
        </div>
      ),
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "testCase",
      accessorKey: "testCase",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            testCase
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => (
        <div className="font-medium text-primary truncate">
          {" "}
          <span title={row.getValue("testCase") || ""}>{row.getValue("testCase") || "-"}</span>
        </div>
      ),
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "dur",
      accessorKey: "dur",
      header: "duration",
      cell: ({ row }) => <div className="truncate">{formatDuration(row.getValue("dur"))}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "platform",
      accessorKey: "platform",
      header: "platform",
      cell: ({ row }) => <div className="truncate">{row.getValue("platform") || "-"}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "physTopo",
      accessorKey: "physTopo",
      header: "physTopo",
      cell: ({ row }) => <div className="truncate">{row.getValue("physTopo") || "-"}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "subTopo",
      accessorKey: "subTopo",
      header: "subTopo",
      cell: ({ row }) => <div className="truncate">{row.getValue("subTopo") || "-"}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "extraKey",
      accessorKey: "extraKey",
      header: "extraKey",
      cell: ({ row }) => <div className="truncate">{row.getValue("extraKey") || "-"}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "status",
      accessorKey: "status",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        let status = row.getValue("status") as string;
        return (
          <Badge
            variant={status === "GHOST" ? "outline" : "default"}
            className={
              status in status_colors
                ? status_colors[status as string]
                : status.startsWith("ROTTEN")
                  ? "bg-yellow-800"
                  : "bg-blue-200"
            }
          >
            {status}
          </Badge>
        );
      },
      enableResizing: true,
      minSize: 50,
      filterFn: statusFilterFn,
    },
    {
      id: "lastP",
      accessorKey: "lastP",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            pass
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const lastP = row.getValue("lastP") as string;
        const lastPLink = (row.original as TestCase).lastPassedLink;

        if (!lastP) return <div className="truncate">-</div>;

        if (lastPLink) {
          return (
            <a
              href={lastPLink}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline flex items-center"
            >
              <span className="truncate">{lastP}</span>
              <ExternalLink className="ml-1 h-3 w-3" />
            </a>
          );
        }

        return <div className="truncate">{lastP}</div>;
      },
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "lastF",
      accessorKey: "lastF",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            fail
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const lastF = row.getValue("lastF") as string;
        const lastFLink = (row.original as TestCase).lastFailedLink;

        if (!lastF) return <div className="truncate">-</div>;

        if (lastFLink) {
          return (
            <a
              href={lastFLink}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline flex items-center"
            >
              <span className="truncate">{lastF}</span>
              <ExternalLink className="ml-1 h-3 w-3" />
            </a>
          );
        }

        return <div className="truncate">{lastF}</div>;
      },
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "date",
      accessorKey: "date",
      header: "date",
      cell: ({ row }) => <div className="truncate">{row.getValue("date")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "passC",
      accessorKey: "passC",
      header: "passC",
      cell: ({ row }) => <div className="truncate">{row.getValue("passC")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "failC",
      accessorKey: "failC",
      header: "failC",
      cell: ({ row }) => <div className="truncate">{row.getValue("failC")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
  ],
};

export const ACTION_HISTORY_TABLE: TableView = {
  key: "ACTION_HISTORY_TABLE",
  columns: [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableHiding: false,
      enableSorting: false,
      enableResizing: false,
      size: 40,
      minSize: 40,
      maxSize: 40,
    },
    {
      id: "date",
      accessorKey: "date",
      header: "date",
      cell: ({ row }) => <div className="truncate">{row.getValue("date")}</div>,
      enableResizing: true,
      minSize: 50,
    },

    {
      id: "status",
      accessorKey: "status",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        let status = row.getValue("status") as string;
        return (
          <Badge
            variant={status === "GHOST" ? "outline" : "default"}
            className={
              status in status_colors
                ? status_colors[status as string]
                : status.startsWith("ROTTEN")
                  ? "bg-yellow-800"
                  : "bg-blue-200"
            }
          >
            {status}
          </Badge>
        );
      },
      enableResizing: true,
      minSize: 50,
      filterFn: statusFilterFn,
    },

    {
      id: "owner",
      accessorKey: "owner",
      header: "owner",
      cell: ({ row }) => <div className="truncate">{row.getValue("owner")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "reason",
      accessorKey: "reason",
      header: "reason",
      cell: ({ row }) => <div className="truncate">{row.getValue("reason")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "action",
      accessorKey: "action",
      header: "action",
      cell: ({ row }) => <div className="truncate">{row.getValue("action")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "remarks",
      accessorKey: "remarks",
      header: "remarks",
      cell: ({ row }) => <div className="truncate">{row.getValue("remarks")}</div>,
      enableResizing: true,
      minSize: 50,
    },
  ],
};

export const RUN_HISTORY_TABLE: TableView = {
  key: "RUN_HISTORY_TABLE",
  columns: [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableHiding: false,
      enableSorting: false,
      enableResizing: false,
      size: 40,
      minSize: 40,
      maxSize: 40,
    },
    {
      id: "branch",
      accessorKey: "branch",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            branch
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="truncate">{row.getValue("branch")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "version",
      accessorKey: "version",
      header: "version",
      cell: ({ row }) => (
        <div className="text-primary truncate cursor-pointer" onClick={() => window.open(row.original.link, "_blank")}>
          {row.getValue("version")}
        </div>
      ),
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "testbed",
      accessorKey: "testbed",
      header: "testbed",
      cell: ({ row }) => <div className="truncate">{row.getValue("testbed")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "status",
      accessorKey: "status",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent w-full justify-start cursor-pointer"
          >
            status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        let status = row.getValue("status") as string;
        return (
          <Badge
            variant={status === "GHOST" ? "outline" : "default"}
            className={
              status in status_colors
                ? status_colors[status as string]
                : status.startsWith("ROTTEN")
                  ? "bg-yellow-800"
                  : "bg-blue-200"
            }
          >
            {status}
          </Badge>
        );
      },
      enableResizing: true,
      minSize: 50,
      filterFn: statusFilterFn,
    },

    {
      id: "dur",
      accessorKey: "dur",
      header: "duration",
      cell: ({ row }) => <div className="truncate">{formatDuration(row.getValue("dur"))}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "remarks",
      accessorKey: "remarks",
      header: "runRemarks",
      cell: ({ row }) => <div className="truncate">{row.getValue("remarks")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "errorClass",
      accessorKey: "errorClass",
      header: "errorClass",
      cell: ({ row }) => <div className="truncate">{row.getValue("errorClass")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "physTopo",
      accessorKey: "physTopo",
      header: "physTopo",
      cell: ({ row }) => <div className="truncate">{row.getValue("physTopo")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "subTopo",
      accessorKey: "subTopo",
      header: "subTopo",
      cell: ({ row }) => <div className="truncate">{row.getValue("subTopo")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "args",
      accessorKey: "args",
      header: "args",
      cell: ({ row }) => <div className="truncate">{row.getValue("args")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "date",
      accessorKey: "date",
      header: "date",
      cell: ({ row }) => <div className="truncate">{row.getValue("date")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "isRbuild",
      accessorKey: "isRbuild",
      header: "isRbuild",
      cell: ({ row }) => <div className="truncate">{row.getValue("isRbuild")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "isPbuild",
      accessorKey: "isPbuild",
      header: "isPbuild",
      cell: ({ row }) => <div className="truncate">{row.getValue("isPbuild")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "bossKey",
      accessorKey: "bossKey",
      header: "bossKey",
      cell: ({ row }) => <div className="truncate">{row.getValue("bossKey")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
  ],
};

export const TESTRESOURCES_TABLE: TableView = {
  key: "TESTRESOURCES_TABLE",
  columns: [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableHiding: false,
      enableSorting: false,
      enableResizing: false,
      size: 40,
      minSize: 40,
      maxSize: 40,
    },
    {
      id: "date",
      accessorKey: "date",
      header: "date",
      cell: ({ row }) => <div className="truncate">{row.getValue("date")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "dutlist",
      accessorKey: "dutlist",
      header: "dutlist",
      cell: ({ row }) => <div className="truncate">{row.getValue("dutlist")}</div>,
      enableResizing: true,
      minSize: 50,
    },
    {
      id: "stcHw",
      accessorKey: "stcHw",
      header: "stcHw",
      cell: ({ row }) => <div className="truncate">{row.getValue("stcHw")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "stcVm",
      accessorKey: "stcVm",
      header: "stcVm",
      cell: ({ row }) => <div className="truncate">{row.getValue("stcVm")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "ls",
      accessorKey: "ls",
      header: "ls",
      cell: ({ row }) => <div className="truncate">{row.getValue("ls")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "IxHw",
      accessorKey: "IxHw",
      header: "IxHw",
      cell: ({ row }) => <div className="truncate">{row.getValue("IxHw")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "IxVm",
      accessorKey: "IxVm",
      header: "IxVm",
      cell: ({ row }) => <div className="truncate">{row.getValue("IxVm")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "IxRouter",
      accessorKey: "IxRouter",
      header: "IxRouter",
      cell: ({ row }) => <div className="truncate">{row.getValue("IxRouter")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "IxNet",
      accessorKey: "IxNet",
      header: "IxNet",
      cell: ({ row }) => <div className="truncate">{row.getValue("IxNet")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "bp",
      accessorKey: "bp",
      header: "bp",
      cell: ({ row }) => <div className="truncate">{row.getValue("bp")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
    {
      id: "bb",
      accessorKey: "bb",
      header: "bb",
      cell: ({ row }) => <div className="truncate">{row.getValue("bb")}</div>,
      enableResizing: true,
      minSize: 50,
      filterFn: numericFilterFn,
    },
  ],
};
