export const formatDuration = (dur: number): string => {
  const hours = Math.floor(dur / 3600);
  const minutes = Math.floor((dur % 3600) / 60);
  const seconds = dur % 60;

  // Format each component to ensure two digits
  const formattedHours = hours.toString().padStart(2, "0");
  const formattedMinutes = minutes.toString().padStart(2, "0");
  const formattedSeconds = seconds.toString().padStart(2, "0");

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};

export const status_colors: Record<string, string> = {
  FAILED: "bg-red-500",
  "FAILED-1": "bg-red-600",
  "FAILED-2": "bg-red-500",
  "FAILED-3": "bg-amber-500",
  "FAILED-4": "bg-amber-400",
  "FAILED-5": "bg-red-500",
  "FAILED-6": "bg-red-600",
  "FAILED-7": "bg-gray-100",
  GHOST: "bg-blue-100",
  OUT: "bg-gray-300",
  PASSED: "bg-green-400",
};
