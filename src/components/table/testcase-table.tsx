"use client";

import { useState, useEffect, useRef } from "react";
import {
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  type ColumnSizingState,
  type ColumnOrderState,
  type Row,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Download, Eye, RefreshCw } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ColumnResizer } from "../column-resizer";
import { ColumnOrderDropdown } from "../column-ordering";
import { ColumnFilterDropdown } from "@/components/column-filter-dropdown";
import { reglevelOptions, statusOptions } from "../search-form";
import { mkConfig, generateCsv, download } from "export-to-csv";
import { TableView } from "./table_views";
import {
  getColumnOrder,
  getColumnSizing,
  getColumnVisibility,
  getRowsPerPage,
  saveColumnOrder,
  saveColumnSizing,
  saveColumnVisibility,
  saveRowsPerPage,
} from "@/lib/utils";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "../ui/label";
import { Combobox } from "../combo-box";
import { useOwnerQuery, useTagsQuery } from "@/api/queries";

// Define the data type for our table
export type TestCase = {
  id: string;
  branch: string;
  suite: string;
  testCase: string;
  dur: string;
  lastChange: string;
  status: string;
  lastP: string;
  lastF: string;
  lastTopo: string;
  owner: string;
  delegate: string;
  reason: string;
  action: string;
  passC: number;
  failC: number;
  regLevel: string;
  errorString: string;
  lastPassedLink: string;
  lastFailedLink: string;
  platform: string;
  args: string;
  testbed: string;
  extraKey: string;
  physTopo: string;
  date: string;
  subTopo: string;
  remarks: string;
  version: string;
  tasId: number;
  branch_id: number;
  stcHw: number;
  stcVm: number;
  ls: number;
  IxHw: number;
  IxVm: number;
  IxRouter: number;
  IxNet: number;
  bp: number;
  bb: number;
  dutlist: string;
  isPbuild: number;
  isRbuild: number;
  bossKey: number;
  link: string;
  errorClass: string;
  DT_RowId: string;
  tag: string;
};

type TestCaseTableProps = {
  data: TestCase[];
  view: TableView;
  title: string;
};

export function TestCaseTable({ data, view, title }: TestCaseTableProps) {
  let { columns, key } = view;
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(getColumnVisibility(key));
  const [rowSelection, setRowSelection] = useState({});
  const [globalFilter, setGlobalFilter] = useState("");
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const [colSizing, setColSizing] = useState<ColumnSizingState>(getColumnSizing(key));
  const [columnOrder, setColumnOrder] = useState<ColumnOrderState>(getColumnOrder(key));
  const [showEdit, setShowEdit] = useState(false);
  const [ownerValue, setOwnerValue] = useState("");
  const [delegateValue, setDelegateValue] = useState("");
  const [tagValue, setTagValue] = useState("");
  const [reason, setReason] = useState("");

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: getRowsPerPage(key),
  });

  // Function to export table data to Excel-compatible CSV
  // Function to export table data to Excel-compatible CSV
  const csvConfig = mkConfig({
    fieldSeparator: ",",
    filename: "sample", // export file name (without .csv)
    decimalSeparator: ".",
    useKeysAsHeaders: true,
  });

  // export function
  // Note: change _ in Row<_>[] with your Typescript type.
  const exportExcel = (rows: Row<TestCase>[]) => {
    const rowData = rows.map((row) => row.original);
    const csv = generateCsv(csvConfig)(rowData);
    download(csvConfig)(csv);
  };

  // Function to render the appropriate filter component based on column ID
  const renderFilterComponent = (header: any) => {
    const columnId = header.id;

    // Skip the select column
    if (columnId === "select") return null;

    // Use select dropdowns for columns with limited options
    switch (columnId) {
      case "status":
        return <ColumnFilterDropdown column={header.column} options={statusOptions} placeholder="Filter status" />;
      case "regLevel":
        return <ColumnFilterDropdown column={header.column} options={reglevelOptions} placeholder="Filter regLevel" />;
      default:
        // Use text input for other columns
        return (
          <Input
            placeholder={`Filter ${columnId}...`}
            value={(header.column.getFilterValue() as string) ?? ""}
            onChange={(event) => header.column.setFilterValue(event.target.value)}
            className="h-7 text-xs"
          />
        );
    }
  };

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    columnResizeMode: "onChange",
    enableColumnResizing: true,
    onColumnSizingChange: (updater) => {
      const newSizing = typeof updater === "function" ? updater(colSizing) : updater;

      setColSizing(newSizing);
    },
    onColumnOrderChange: (newOrder) => {
      setColumnOrder(newOrder);
    },
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
      columnOrder,
      pagination,
      columnSizing: colSizing,
    },
  });

  useEffect(() => {
    saveColumnSizing(key, colSizing);
  }, [colSizing]);

  useEffect(() => {
    saveColumnOrder(key, columnOrder);
  }, [columnOrder]);

  useEffect(() => {
    saveColumnVisibility(key, columnVisibility);
  }, [columnVisibility]);

  useEffect(() => {
    saveRowsPerPage(key, pagination.pageSize);
  }, [pagination]);

  const call_to_deditor = (edit: boolean) => {
    let action = edit ? "edit" : "remove";
    let url = "/api/deditor";
    let selectedRows = table.getSelectedRowModel().rows.map((row) => row.original);

    let formData = new FormData();
    formData.append("action", action);

    // change selected rows into the desired format
    selectedRows.forEach((r: TestCase) => {
      if (edit) {
        formData.append(`data[${r.DT_RowId}][owner]`, ownerValue);
        formData.append(`data[${r.DT_RowId}][delegate]`, delegateValue);
        formData.append(`data[${r.DT_RowId}][reason]`, reason);
        formData.append(`data[${r.DT_RowId}][tag]`, tagValue);
      } else {
        formData.append(`data[${r.DT_RowId}][DT_RowId]`, r.DT_RowId);
      }
    });

    if (import.meta.env.MODE !== "development") {
      fetch("/api", {
        method: "GET",
        credentials: "include",
        headers: {
          "X-CSRF-Token": "Fetch",
        },
      }).then((res) => {
        fetch(url, {
          method: "POST",
          credentials: "include",
          headers: {
            "X-CSRF-Token": res.headers.get("X-CSRF-Token") as string,
          },
          body: formData,
        }).then(() => {
          setShowEdit(false);
          window.location.reload();
        });
      });
    }
  };

  return (
    <div className="w-full">
      <div className="rounded-md border">
        {showEdit && (
          <Dialog open={showEdit} onOpenChange={setShowEdit}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Edit Entry</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Owner
                  </Label>
                  <Combobox
                    onChange={setOwnerValue}
                    queryHook={useOwnerQuery}
                    type="user"
                    value={ownerValue}
                    placeholder="Select..."
                    className="w-full col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Delegate
                  </Label>
                  <Combobox
                    onChange={setDelegateValue}
                    queryHook={useOwnerQuery}
                    type="user"
                    value={delegateValue}
                    placeholder="Select..."
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Reason
                  </Label>
                  <Select value={reason} onValueChange={setReason}>
                    <SelectTrigger className="w-full col-span-3">
                      <SelectValue placeholder="Select..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dts">dts</SelectItem>
                      <SelectItem value="infrastructure">infrastructure</SelectItem>
                      <SelectItem value="script error">script error</SelectItem>
                      <SelectItem value="other">other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Tag
                  </Label>
                  <Combobox
                    onChange={setTagValue}
                    queryHook={useTagsQuery}
                    type="string"
                    value={tagValue}
                    placeholder="Select..."
                    className="w-full col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" onClick={() => call_to_deditor(true)}>
                  Edit
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
        <div className="bg-primary p-2 text-white font-semibold rounded-t-md">{title}</div>
        <div className="flex items-center justify-between p-2 border-b flex-wrap gap-2">
          <div className="flex flex-1 items-center space-x-2 flex-wrap gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-8 gap-1">
                  <Eye className="h-3.5 w-3.5" />
                  Column visibility
                  <ChevronDown className="h-3.5 w-3.5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[200px]">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </DropdownMenuContent>
              <ColumnOrderDropdown table={table} />
            </DropdownMenu>
            <Button
              variant="outline"
              size="sm"
              className="h-8 gap-1"
              onClick={() => exportExcel(table.getCoreRowModel().rows)}
            >
              <Download className="h-3.5 w-3.5" />
              Export CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={() => {
                const visibleRows = table.getRowModel().rows;
                visibleRows.forEach((row) => row.toggleSelected(true));
              }}
            >
              Select Visible
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={() => {
                const newSelection: Record<string, boolean> = {};
                table.getCoreRowModel().rows.forEach((row) => {
                  newSelection[row.id] = true;
                });
                setRowSelection(newSelection);
              }}
            >
              Select all
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={() => {
                const newSelection: Record<string, boolean> = {};
                table.getCoreRowModel().rows.forEach((row) => {
                  newSelection[row.id] = false;
                });
                setRowSelection(newSelection);
              }}
            >
              Deselect all
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={() => {
                const newSelection: Record<string, boolean> = {};
                table.getFilteredRowModel().rows.forEach((row) => {
                  newSelection[row.id] = !row.getIsSelected();
                });
                setRowSelection(newSelection);
              }}
            >
              Invert Select
            </Button>
            <Button variant="outline" size="sm" className="h-8 gap-1">
              <RefreshCw className="h-3.5 w-3.5" />
              makeRegressCmdForSelected
            </Button>
            <Button variant="outline" size="sm" className="h-8" onClick={() => call_to_deditor(false)}>
              Delete
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={() => table.getSelectedRowModel().rows.length > 0 && setShowEdit(true)}
            >
              Edit
            </Button>
          </div>
          <div className="flex items-center gap-2 flex-wrap">
            <div className="flex items-center gap-1">
              <span className="text-sm">Search:</span>
              <Input
                placeholder="Search all columns..."
                value={globalFilter ?? ""}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="h-8 w-[200px]"
              />
            </div>
          </div>
        </div>
        <div ref={tableContainerRef} className="rounded-md border">
          <Table className="resizable-table" style={{ width: table.getCenterTotalSize() }}>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        key={header.id}
                        className={`px-2 py-2 ${header.id === "select" ? "select-column" : "data-column"} relative`}
                        style={{
                          width: header.getSize(),
                        }}
                      >
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                        <ColumnResizer header={header} />
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
              <TableRow>
                {/* Filter row */}
                {table.getHeaderGroups()[0].headers.map((header) => (
                  <TableHead
                    key={`filter-${header.id}`}
                    className={`p-1 ${header.id === "select" ? "select-column" : "data-column"}`}
                  >
                    {renderFilterComponent(header)}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        className={`px-2 py-2 ${cell.column.id === "select" ? "select-column" : "data-column"}`}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-between p-2 border-t">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s)
            selected.
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Rows per page</p>
              <Select
                value={`${table.getState().pagination.pageSize}`}
                onValueChange={(value) => {
                  table.setPageSize(Number(value));
                }}
              >
                <SelectTrigger className="h-8 w-[85px]">
                  <SelectValue placeholder={table.getState().pagination.pageSize} />
                </SelectTrigger>
                <SelectContent side="top">
                  {[5, 10, 20, 30, 40, 50, 100, 200, 400].map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">Go to first page</span>
                <span>{"<<"}</span>
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">Go to previous page</span>
                <span>{"<"}</span>
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">Go to next page</span>
                <span>{">"}</span>
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">Go to last page</span>
                <span>{">>"}</span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
