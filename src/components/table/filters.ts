// Custom filter function for numeric columns
export const numericFilterFn = (row: any, columnId: string, filterValue: string) => {
  const rowValue = row.getValue(columnId) as number;

  // If filter is empty, show all rows
  if (!filterValue.trim()) return true;

  // Try to parse the filter value as a number
  const filterNumber = Number.parseInt(filterValue, 10);

  // If it's not a valid number, fall back to default filtering
  if (isNaN(filterNumber)) {
    return String(rowValue).includes(filterValue);
  }

  // Compare the actual numbers
  return rowValue === filterNumber;
};

// Custom filter function for numeric columns
export const statusFilterFn = (row: any, _: string, filterValue: string) => {
  const rowValue = row.getValue("status") as string;

  // If filter is empty, show all rows
  if (!filterValue.trim()) return true;

  // Try to parse the filter value as a number
  switch (filterValue) {
    case "PASSED":
    case "GHOST":
    case "OUT":
    case "SKIPPED":
      return rowValue === filterValue;

    case "!PASSED":
    case "!GHOST":
    case "!OUT":
    case "!SKIPPED":
      return rowValue !== filterValue.slice(1);
    case "!FAILED":
    case "!ROTTEN":
      return !rowValue.startsWith(filterValue.slice(1));

    case "ROTTEN":
    case "FAILED":
      return rowValue.startsWith(filterValue);

    default:
      return false;
  }
};
