"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { InfoIcon } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useContext } from "react";
import UserContext from "@/context/UserContext";

export function ProfileForm() {
  const userCtx = useContext(UserContext);

  const profileData = {
    email: userCtx?.email || "",
    fullName: userCtx?.fullName || "",
    category: userCtx?.category || "",
    project: userCtx?.project || [],
    location: userCtx?.location || "",
    teams: [userCtx?.team1, userCtx?.team2, userCtx?.team3, userCtx?.team4].every(
      (e) => e == null || e == undefined || e == ""
    )
      ? []
      : [userCtx?.team1, userCtx?.team2, userCtx?.team3, userCtx?.team4],
    hideStatusOutCases: userCtx?.hideStatusOutCases || false,
    hideRegressIncludeFalseAndBetaTestCases: userCtx?.hideRegressIncludeFalseAndBetaTestCases || false,
    emailNotifications: userCtx?.emailNotifications || false,
    hyperlinkToTestCases: userCtx?.hyperlinkToTestCases || false,
    wantToSeeIntraDayDeltaMails: userCtx?.wantToSeeIntraDayDeltaMails || false,
    doNotShowGhostExpandedTopos: userCtx?.doNotShowGhostExpandedTopos || false,
    aggregateVMSimTopo: userCtx?.aggregateVMSimTopo || false,
    shortQuickMyErrors: userCtx?.shortQuickMyErrors || false,
    showHiddenBranches: userCtx?.showHiddenBranches || false,
    displayTableSize: userCtx?.displayTableSize || 200,
    ccList: userCtx?.ccList || "",
    inCcListOf: userCtx?.inCcListOf || "",
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
          <CardDescription>Your basic profile information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <div className="flex items-center gap-2">
                <Input id="email" value={profileData.email} readOnly className="bg-muted" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fullName">Full Name</Label>
              <Input id="fullName" value={profileData.fullName} readOnly className="bg-muted" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Input id="category" value={profileData.category} readOnly className="bg-muted" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="project">Project</Label>
              <div className="flex items-center gap-2">
                <Input id="project" value={profileData.project.join(",")} readOnly className="bg-muted" />
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Will drive which branches you will see in your environment</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input id="location" value={profileData.location} readOnly className="bg-muted" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="teams">Member of Teams</Label>
              <div className="flex flex-wrap gap-2">
                {profileData.teams.map((team) => {
                  if (team !== "" && team != null) {
                    return (
                      <Badge key={team} variant="secondary">
                        {team}
                      </Badge>
                    );
                  }
                })}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Display Preferences</CardTitle>
          <CardDescription>Configure how test cases and results are displayed</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="hideStatusOutCases">Hide status OUT cases</Label>
                <p className="text-sm text-muted-foreground">If set, testCases in status OUT will not be displayed</p>
              </div>
              <Switch id="hideStatusOutCases" checked={profileData.hideStatusOutCases} disabled />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="hideRegressIncludeFalseAndBetaTestCases">
                  Hide regressInclude false and beta test cases
                </Label>
                <p className="text-sm text-muted-foreground">
                  If set, regressInclude beta or false testCases and suites will not be displayed, unless the
                  regressInclude searchbox is selected
                </p>
              </div>
              <Switch
                id="hideRegressIncludeFalseAndBetaTestCases"
                checked={profileData.hideRegressIncludeFalseAndBetaTestCases}
                disabled
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="emailNotifications">Email notifications</Label>
                <p className="text-sm text-muted-foreground">
                  If set, you will receive emails for each edit on the regressiondb
                </p>
              </div>
              <Switch id="emailNotifications" checked={profileData.emailNotifications} disabled />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="hyperlinkToTestCases">Hyperlink to test cases</Label>
                <p className="text-sm text-muted-foreground">
                  If set, the links to a run will bring you to the testCase else to the main run page
                </p>
              </div>
              <Switch id="hyperlinkToTestCases" checked={profileData.hyperlinkToTestCases} disabled />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="wantToSeeIntraDayDeltaMails">Want to see intra-day delta mails</Label>
                <p className="text-sm text-muted-foreground">
                  If set, you will receive an e-mail if your tests are failing during the day
                </p>
              </div>
              <Switch id="wantToSeeIntraDayDeltaMails" checked={profileData.wantToSeeIntraDayDeltaMails} disabled />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="doNotShowGhostExpandedTopos">Do not show ghost expanded topos</Label>
                <p className="text-sm text-muted-foreground">
                  If set, topoStatus GHOST's will be hidden if they result from the topoExpand scripts unless unchecked
                  in the query window
                </p>
              </div>
              <Switch id="doNotShowGhostExpandedTopos" checked={profileData.doNotShowGhostExpandedTopos} disabled />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="aggregateVMSimTopo">Aggregate VM sim topo's</Label>
                <p className="text-sm text-muted-foreground">
                  If set, all topo lines related to sim VM results will be shown aggregated
                </p>
              </div>
              <Switch id="aggregateVMSimTopo" checked={profileData.aggregateVMSimTopo} disabled />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="shortQuickMyErrors">Short / quick myErrors</Label>
                <p className="text-sm text-muted-foreground">
                  If set, myerrors will only dump the errors table, and not the known errors table
                </p>
              </div>
              <Switch id="shortQuickMyErrors" checked={profileData.shortQuickMyErrors} disabled />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="showHiddenBranches">Show hidden branches</Label>
                <p className="text-sm text-muted-foreground">If set, the GUI will show old (deprecated) branches</p>
              </div>
              <Switch id="showHiddenBranches" checked={profileData.showHiddenBranches} disabled />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Additional Settings</CardTitle>
          <CardDescription>Configure table size and notification preferences</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="displayTableSize">Display table size</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="displayTableSize"
                  type="number"
                  value={profileData.displayTableSize}
                  readOnly
                  className="bg-muted"
                />
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Sets the number of lines you want to see for store2db tables (default 200 lines)
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="ccList">CC List</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="ccList"
                  value={profileData.ccList}
                  readOnly
                  className="bg-muted"
                  placeholder="No CC list configured"
                />
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        List people who want to be notified of your results AS well. Use comma's to separate emails
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="inCcListOf">In CC List Of</Label>
              <Input
                id="inCcListOf"
                value={profileData.inCcListOf}
                readOnly
                className="bg-muted"
                placeholder="Not in any CC list"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
