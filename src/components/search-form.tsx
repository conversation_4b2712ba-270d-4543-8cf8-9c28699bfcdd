'use client';

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { Search, X } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
// import { Branch } from "@/api";
import { Combobox } from "@/components/combo-box";
import { useOwnerQuery, useParentSuiteQuery, useSuiteQuery } from "@/api/queries";
import { useQueryClient, useQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import { Input } from "./ui/input";
import { Checkbox } from "@/components/ui/checkbox";

type SearchFormProps = {
  branches: string[];
};

export type Option = {
  name: string;
  value: string;
};

export const statusOptions: Option[] = [
  { name: "PASSED", value: "PASSED" },
  { name: "!PASSED", value: "!PASSED" },
  { name: "FAILED", value: "FAILED" },
  { name: "!FAILED", value: "!FAILED" },
  { name: "ROTTEN", value: "ROTTEN" },
  { name: "!ROTTEN", value: "!ROTTEN" },
  { name: "GHOST", value: "GHOST" },
  { name: "!GHOST", value: "!GHOST" },
  { name: "OUT", value: "OUT" },
  { name: "!OUT", value: "!OUT" },
  { name: "SKIPPED", value: "SKIPPED" },
  { name: "!SKIPPED", value: "!SKIPPED" },
];

export const actionOptions: Option[] = [
  { name: "empty", value: "empty" },
  { name: "!empty", value: "!empty" },
];

export const reglevelOptions: Option[] = [
  { name: "quickOnly", value: "quickOnly" },
  { name: "mediumOnly", value: "mediumOnly" },
  { name: "regularOnly", value: "regularOnly" },
  { name: "extensiveOnly", value: "extensiveOnly" },
  { name: "extremeOnly", value: "extremeOnly" },
  { name: "express", value: "express" },
  { name: "quick", value: "quick" },
  { name: "medium", value: "medium" },
  { name: "regular", value: "regular" },
  { name: "extensive", value: "extensive" },
  { name: "extreme", value: "extreme" },
  { name: "always", value: "always" },
];

// Some lightweight option sets for selects that have no API yet
const noneOrSelect: Option[] = [
  { name: "None", value: "None" },
];

const infoLevelOptions: Option[] = [
  { name: "testOnly", value: "testOnly" },
  { name: "full", value: "full" },
];

export function SearchForm({ branches }: SearchFormProps) {
  useQueryClient();

  const [_, setActiveTab] = useState("basic");

  // Basic tab
  const [parentSuiteValue, setParentSuiteValue] = useState("");
  const [ownerValue, setOwnerValue] = useState("");
  const [suiteValue, setSuiteValue] = useState("");
  const [branch, setBranch] = useState("");
  const [status, setStatus] = useState("");
  const [action, setAction] = useState("");
  const [reglevel, setReglevel] = useState("");
  const [testcase, setTestCase] = useState("");

  // Advanced tab — left column
  const [ownerAdv, setOwnerAdv] = useState("");
  const [reason, setReason] = useState("");
  const [build, setBuild] = useState("");
  const [physiology, setPhysiology] = useState("");
  const [platform, setPlatform] = useState("");
  const [vmPlatform, setVmPlatform] = useState("None");
  const [subSystem, setSubSystem] = useState("");
  const [dumpPerTopo, setDumpPerTopo] = useState(false);
  const [caseInsensitive, setCaseInsensitive] = useState(false);

  // Advanced tab — middle column
  const [errorClassSeverity, setErrorClassSeverity] = useState("");
  const [tag, setTag] = useState("");
  const [team, setTeam] = useState("");
  const [beforeBuild, setBeforeBuild] = useState("");
  const [subTopology, setSubTopology] = useState("");
  const [topoStatus, setTopoStatus] = useState("");
  const [vmHypervisor, setVmHypervisor] = useState("None");
  const [subSystemBranch, setSubSystemBranch] = useState("");
  const [skipExpandAndGhost, setSkipExpandAndGhost] = useState(true);
  const [vmTopoAggr, setVmTopoAggr] = useState(false);

  // Advanced tab — right column
  const [infoLevel, setInfoLevel] = useState("testOnly");
  const [framework, setFramework] = useState("");
  const [lastChangedSince, setLastChangedSince] = useState(""); // yyyy-mm-dd
  const [fid, setFid] = useState("");
  const [pressInclude, setPressInclude] = useState("");
  const [location, setLocation] = useState("");
  const [dts, setDts] = useState("");
  const [afterBuild, setAfterBuild] = useState("");
  const [extraKey, setExtraKey] = useState("");
  const [lastXDays, setLastXDays] = useState("");
  const [vmInterface, setVmInterface] = useState("None");
  const [topoReason, setTopoReason] = useState("");
  const [ignoreInvalidSubtopos, setIgnoreInvalidSubtopos] = useState(false);

  // --- API-backed option lists ---
  const { data: reglevelsData } = useQuery({ queryKey: ["reglevels"], queryFn: async () => (await fetch("/reglevels")).json() });
  const { data: tagsData } = useQuery({ queryKey: ["tags"], queryFn: async () => (await fetch("/tags")).json() });
  const { data: errorClassData } = useQuery({ queryKey: ["errorClass"], queryFn: async () => (await fetch("/errorClass")).json() });
  const { data: extraKeysData } = useQuery({ queryKey: ["extraKeys"], queryFn: async () => (await fetch("/extraKeys")).json() });

  const toOpts = (list: any): Option[] => {
    if (!list) return [];
    const src = Array.isArray(list) ? list : (list.data ?? list.items ?? Object.values(list));
    if (!Array.isArray(src)) return [];
    return src.map((x: any) =>
      typeof x === "string"
        ? { name: x, value: x }
        : { name: x.name ?? x.value ?? String(x), value: x.value ?? x.name ?? String(x) }
    );
  };

  const reglevelsOpts = toOpts(reglevelsData);
  const tagOpts = toOpts(tagsData);
  const errorClassOpts = toOpts(errorClassData);
  const extraKeyOpts = toOpts(extraKeysData);

  const clearState = () => {
    // basic
    setParentSuiteValue("");
    setOwnerValue("");
    setSuiteValue("");
    setBranch("");
    setStatus("");
    setReglevel("");
    setAction("");
    setTestCase("");

    // advanced left
    setOwnerAdv("");
    setReason("");
    setBuild("");
    setPhysiology("");
    setPlatform("");
    setVmPlatform("None");
    setSubSystem("");
    setDumpPerTopo(false);
    setCaseInsensitive(false);

    // advanced middle
    setErrorClassSeverity("");
    setTag("");
    setTeam("");
    setBeforeBuild("");
    setSubTopology("");
    setTopoStatus("");
    setVmHypervisor("None");
    setSubSystemBranch("");
    setSkipExpandAndGhost(true);
    setVmTopoAggr(false);

    // advanced right
    setInfoLevel("testOnly");
    setFramework("");
    setLastChangedSince("");
    setFid("");
    setPressInclude("");
    setLocation("");
    setDts("");
    setAfterBuild("");
    setExtraKey("");
    setLastXDays("");
    setVmInterface("None");
    setTopoReason("");
    setIgnoreInvalidSubtopos(false);
  };

  return (
    <Card className="p-6 pb-24 relative grow">
      <Tabs defaultValue="basic" className="mb-6" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic" className="cursor-pointer">
            Basic Search
          </TabsTrigger>
          <TabsTrigger value="advanced" className="cursor-pointer">
            Advanced Search
          </TabsTrigger>
        </TabsList>

        {/* BASIC */}
        <TabsContent value="basic" className="mt-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="space-y-2">
              <label className="text-sm font-medium">Branch</label>
              <Select value={branch} onValueChange={setBranch}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {branches
                    .filter((b) => b.length > 0)
                    .map((b) => (
                      <SelectItem key={b} value={b}>{b}</SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((s) => (
                    <SelectItem key={s.value} value={s.value}>{s.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Reglevel</label>
              <Select value={reglevel} onValueChange={setReglevel}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {(reglevelsOpts.length ? reglevelsOpts : reglevelOptions).map((r) => (
                    <SelectItem key={r.value} value={r.value}>{r.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Action</label>
              <Select value={action} onValueChange={setAction}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {actionOptions.map((a) => (
                    <SelectItem key={a.value} value={a.value}>{a.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">TestCase</label>
              <Input placeholder="Search..." value={testcase} onChange={(e) => setTestCase(e.target.value)} />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Parent suite</label>
              <Combobox
                placeholder="Select..."
                value={parentSuiteValue}
                onChange={setParentSuiteValue}
                queryHook={useParentSuiteQuery}
                type="string"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Owner</label>
              <Combobox
                placeholder="Select..."
                value={ownerValue}
                onChange={setOwnerValue}
                queryHook={useOwnerQuery}
                type="user"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Suite</label>
              <Combobox
                placeholder="Select..."
                value={suiteValue}
                onChange={setSuiteValue}
                queryHook={useSuiteQuery}
                type="string"
              />
            </div>
          </div>
        </TabsContent>

        {/* ADVANCED */}
        <TabsContent value="advanced" className="mt-4">
          <div className="grid grid-cols-12 gap-5">
            {/* Row 1 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Owner</label>
              <Combobox
                placeholder="Select..."
                value={ownerAdv}
                onChange={setOwnerAdv}
                queryHook={useOwnerQuery}
                type="user"
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Error class severity</label>
              <Select value={errorClassSeverity} onValueChange={setErrorClassSeverity}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {errorClassOpts.map((o) => (
                    <SelectItem key={o.value} value={String(o.value)}>{o.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">InfoLevel</label>
              <Select value={infoLevel} onValueChange={setInfoLevel}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {infoLevelOptions.map((o) => (
                    <SelectItem key={o.value} value={o.value}>{o.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Row 2 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Reason</label>
              <Input placeholder="Select..." value={reason} onChange={(e) => setReason(e.target.value)} />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Tag</label>
              <Select value={tag} onValueChange={setTag}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {tagOpts.map((t) => (
                    <SelectItem key={t.value} value={t.value}>{t.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Framework</label>
              <Input placeholder="Select..." value={framework} onChange={(e) => setFramework(e.target.value)} />
            </div>

            {/* Row 3 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Build</label>
              <Input placeholder="ex: BT-1688. Use [!build] for negative search" value={build} onChange={(e) => setBuild(e.target.value)} />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Physiology</label>
              <Input placeholder="Select..." value={physiology} onChange={(e) => setPhysiology(e.target.value)} />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Team</label>
              <Input placeholder="Select..." value={team} onChange={(e) => setTeam(e.target.value)} />
            </div>

            {/* Row 4 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Platform</label>
              <Input placeholder="Select..." value={platform} onChange={(e) => setPlatform(e.target.value)} />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Before build</label>
              <Input placeholder="ex: BT-1688. Use [!build] for negative search" value={beforeBuild} onChange={(e) => setBeforeBuild(e.target.value)} />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Last changed since</label>
              <Input type="date" value={lastChangedSince} onChange={(e) => setLastChangedSince(e.target.value)} />
            </div>

            {/* Row 5 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">VM platform</label>
              <Select value={vmPlatform} onValueChange={setVmPlatform}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {noneOrSelect.map((o) => (
                    <SelectItem key={o.value} value={o.value}>{o.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Subtopology</label>
              <Input placeholder="Select..." value={subTopology} onChange={(e) => setSubTopology(e.target.value)} />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Pressinclude</label>
              <Input placeholder="Select..." value={pressInclude} onChange={(e) => setPressInclude(e.target.value)} />
            </div>

            {/* Row 6 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Sub system</label>
              <Input placeholder="Select..." value={subSystem} onChange={(e) => setSubSystem(e.target.value)} />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Topo status</label>
              <Input placeholder="Select..." value={topoStatus} onChange={(e) => setTopoStatus(e.target.value)} />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Location</label>
              <Input placeholder="Select..." value={location} onChange={(e) => setLocation(e.target.value)} />
            </div>

            {/* Row 7 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Dts</label>
              <Input placeholder="Select..." value={dts} onChange={(e) => setDts(e.target.value)} />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">After build</label>
              <Input placeholder="ex: BT-1688. Use [!build] for negative search" value={afterBuild} onChange={(e) => setAfterBuild(e.target.value)} />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">VM hypervisor</label>
              <Select value={vmHypervisor} onValueChange={setVmHypervisor}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {noneOrSelect.map((o) => (
                    <SelectItem key={o.value} value={o.value}>{o.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Row 8 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Sub system branch</label>
              <Input placeholder="Select..." value={subSystemBranch} onChange={(e) => setSubSystemBranch(e.target.value)} />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">ExtraKey</label>
              <Select value={extraKey} onValueChange={setExtraKey}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {extraKeyOpts.map((e) => (
                    <SelectItem key={e.value} value={e.value}>{e.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Last x days</label>
              <Input placeholder="e.g. 7" value={lastXDays} onChange={(e) => setLastXDays(e.target.value)} />
            </div>

            {/* Checkbox group at bottom */}
            <div className="col-span-12 pt-4 mt-2 border-t">
              <div className="grid grid-cols-12 gap-4">
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox id="dump-per-topo" checked={dumpPerTopo} onCheckedChange={(v) => setDumpPerTopo(!!v)} />
                  <label htmlFor="dump-per-topo" className="text-sm">Dump per topo</label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox id="case-insensitive" checked={caseInsensitive} onCheckedChange={(v) => setCaseInsensitive(!!v)} />
                  <label htmlFor="case-insensitive" className="text-sm">Case insensitive search</label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox id="skip-expand-ghost" checked={skipExpandAndGhost} onCheckedChange={(v) => setSkipExpandAndGhost(!!v)} />
                  <label htmlFor="skip-expand-ghost" className="text-sm">Skip expand and ghost</label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox id="vm-topo-aggr" checked={vmTopoAggr} onCheckedChange={(v) => setVmTopoAggr(!!v)} />
                  <label htmlFor="vm-topo-aggr" className="text-sm">VmTopoAggr</label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox id="ignore-invalid-subtopos" checked={ignoreInvalidSubtopos} onCheckedChange={(v) => setIgnoreInvalidSubtopos(!!v)} />
                  <label htmlFor="ignore-invalid-subtopos" className="text-sm">IgnoreInvalidSubtopos</label>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="absolute bottom-5 right-5 flex items-center justify-end space-x-2">
        <Button variant="outline" type="button" className="cursor-pointer" onClick={clearState}>
          <X className="mr-2 h-4 w-4" />
          Clear
        </Button>
        <Link
          to={"/results"}
          search={{
            // basic
            branch: branch,
            regLevel: reglevel,
            lastStatus: status,
            ownerTest: ownerValue,
            parentsuite: parentSuiteValue,
            suite: suiteValue,
            action: action,
            testCase: testcase,

            // advanced left
            owner: ownerAdv,
            reason,
            build,
            physiology,
            platform,
            vmPlatform,
            subSystem,
            dumpPerTopo,
            caseInsensitive,

            // advanced middle
            errorClassSeverity,
            tag,
            team,
            beforeBuild,
            subTopology,
            topoStatus,
            vmHypervisor,
            subSystemBranch,
            skipExpandAndGhost,
            vmTopoAggr,

            // advanced right
            infoLevel,
            framework,
            lastChangedSince,
            fid,
            pressInclude,
            location,
            dts,
            afterBuild,
            extraKey,
            lastXDays,
            vmInterface,
            topoReason,
            ignoreInvalidSubtopos,

            cmd: "query",
          }}
        >
          <Button type="submit" className="bg-primary cursor-pointer">
            <Search className="mr-2 h-4 w-4" />
            Search
          </Button>
        </Link>
      </div>
    </Card>
  );
}
