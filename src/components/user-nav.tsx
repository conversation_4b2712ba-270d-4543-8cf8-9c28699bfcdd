import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { User } from "lucide-react";
import { Link } from "@tanstack/react-router";
import { BacklogItem } from "./layout";
import { ParsingBacklogDetails } from "./parsing-backlog-details";
import { useState } from "react";

interface UserNavProps {
  name: string;
  status: string;
  backlogCount: number;
  backlogItems: BacklogItem[];
}

export function UserNav({ name, status, backlogItems, backlogCount }: UserNavProps) {
  const initials = name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .substring(0, 2);

  const [open, setOpen] = useState(false);

  return (
    <div className="flex items-center gap-4">
      {backlogItems.length > 0 ? (
        <ParsingBacklogDetails backlogCount={backlogCount} backlogItems={backlogItems} />
      ) : (
        <span className="hidden text-sm text-muted-foreground md:inline-block">{status}</span>
      )}
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="relative h-8 w-8 rounded-full cursor-pointer"
            onClick={() => setOpen(!open)}
          >
            <Avatar className="h-8 w-8">
              <AvatarFallback>{initials}</AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{name}</p>
              <p className="text-xs leading-none text-muted-foreground">{status}</p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <Link to="/profile" className="flex gap-1 items-center" onClick={() => setOpen(false)}>
              <User className="h-4 w-4" />
              <span>Profile</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
