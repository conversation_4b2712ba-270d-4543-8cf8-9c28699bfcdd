import { MainNav } from "@/components/main-nav";
import { UserNav } from "@/components/user-nav";
import { Link } from "@tanstack/react-router";
import { useContext, useEffect, useState } from "react";
import UserContext from "@/context/UserContext";
import { useBacklogsQuery } from "@/api/queries";

type LayoutProps = {
  children: React.ReactElement;
};

export interface BacklogItem {
  id: string;
  duration: string;
}

export default function Layout({ children }: LayoutProps) {
  const [parseBacklogs, setParseBacklogs] = useState(0);
  const [parseBacklogsItems, setParseBacklogsItems] = useState<BacklogItem[]>([]);
  const { data } = useBacklogsQuery();
  const userCtx = useContext(UserContext);

  useEffect(() => {
    if (data && data.backlogCount) {
      setParseBacklogs(data.backlogCount);
    }

    if (userCtx && userCtx.adminUser && data && data.backlogItems) {
      let arr: BacklogItem[] = [];
      for (const [key, value] of Object.entries(data.backlogItems)) {
        arr.push({
          id: key,
          duration: value,
        });
      }
      setParseBacklogsItems(arr);
    }
  }, [data]);

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 border-b bg-background">
        <div className="flex h-16 items-center justify-between p-6">
          <div className="flex items-center gap-6">
            <Link to={"/"} className="cursor-pointer">
              <h1 className="text-3xl font-bold tracking-tight text-primary">regressionDB</h1>
            </Link>
            <MainNav />
          </div>
          <UserNav
            name={userCtx?.fullName || ""}
            status={`Parsing backLog: ${parseBacklogs}`}
            backlogItems={parseBacklogsItems}
            backlogCount={parseBacklogs}
          />
        </div>
      </header>
      <main className="flex flex-col grow p-4 sm:p-6 bg-gray-50">{children}</main>
      <footer className="border-t py-4 px-6">
        <div className="flex items-center justify-between">
          <p className="text-md text-muted-foreground">Nokia ION Confidential and Proprietary</p>
          <p className="text-md text-muted-foreground">© {new Date().getFullYear()} Nokia</p>
        </div>
      </footer>
    </div>
  );
}
