import { useQuery } from "@tanstack/react-query";
import { getOwnerResults, getParentSuiteResults, getSuiteResults, getUserProfile, getBacklogs, getTags } from ".";

export function useOwnerQuery(searchTerm: string) {
  return useQuery({
    queryKey: ["owner", searchTerm],
    queryFn: () => getOwnerResults(searchTerm),
  });
}

export function useParentSuiteQuery(searchTerm: string) {
  return useQuery({
    queryKey: ["parentsuite", searchTerm],
    queryFn: () => getParentSuiteResults(searchTerm),
  });
}

export function useTagsQuery(searchTerm: string) {
  return useQuery({
    queryKey: ["tags", searchTerm],
    queryFn: () => getTags(searchTerm),
  });
}

export function useSuiteQuery(searchTerm: string) {
  return useQuery({
    queryKey: ["suite", searchTerm],
    queryFn: () => getSuiteResults(searchTerm),
  });
}

export function useProfileQuery() {
  return useQuery({
    queryKey: ["profile"],
    queryFn: () => getUserProfile(),
  });
}

export function useBacklogsQuery() {
  return useQuery({
    queryKey: ["backlogs"],
    queryFn: () => getBacklogs(),
    refetchInterval: 120000,
  });
}
