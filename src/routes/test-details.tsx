import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TestCaseTable } from "@/components/table/testcase-table";
import { SearchParams, searchSchema } from "./results";
import { getActionHistory, getRunHistory, getSearchResults, getTestResources } from "@/api";
import { Spinner } from "@/components/ui/spinner";
import { isError } from "@/lib/utils";
import {
  ACTION_HISTORY_TABLE,
  RESULTS_PER_TOPO_TABLE,
  RUN_HISTORY_TABLE,
  TESTRESOURCES_TABLE,
} from "@/components/table/table_views";
import { useRouter, useCanGoBack } from "@tanstack/react-router";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";

export const Route = createFileRoute("/test-details")({
  component: RouteComponent,
  pendingComponent: () => (
    <div className="w-full grow flex justify-center items-center">
      <Spinner size="large" className="text-primary" />
    </div>
  ),
  validateSearch: (search: Record<string, unknown>) => searchSchema.parse(search),
  loaderDeps: ({ search }) => {
    return {
      branch: search.branch,
      branch_id: search.branch_id,
      tasId: search.tasId,
      testCase: search.testCase,
      dumpPerTopo: 1,
    };
  },
  loader: async ({ deps }) => {
    return {
      data: await getSearchResults(deps as SearchParams),
      actionHistoryData: await getActionHistory({
        tasId: deps.tasId,
      }),
      testResourcesData: await getTestResources({
        tasId: deps.tasId,
      }),
      runHistoryData: await getRunHistory({
        tasId: deps.tasId,
        branch_id: deps.branch_id,
      }),
    };
  },
});

function RouteComponent() {
  const [_, setActiveTab] = useState("results_per_topology");

  const { data, actionHistoryData, testResourcesData, runHistoryData } = Route.useLoaderData();
  const router = useRouter();
  const canGoBack = useCanGoBack();

  return (
    <>
      {canGoBack && (
        <Button
          className={"rounded-full shadow-lg transition-all hover:scale-102 hover:bg-accent w-max mb-5 cursor-pointer"}
          variant="outline"
          onClick={() => router.history.back()}
        >
          <ChevronLeft />
        </Button>
      )}
      <Tabs defaultValue="results_per_topology" className="mb-6" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="results_per_topology" className="cursor-pointer">
            RESULTS PER TOPOLOGY
          </TabsTrigger>
          <TabsTrigger value="action_history" className="cursor-pointer">
            ACTIONHISTORY TABLE
          </TabsTrigger>
          <TabsTrigger value="run_history" className="cursor-pointer">
            RUNHISTORY TABLE
          </TabsTrigger>
          <TabsTrigger value="test_resources" className="cursor-pointer">
            TESTRESOURCES TABLE
          </TabsTrigger>
        </TabsList>
        <TabsContent value="results_per_topology" className="mt-4">
          {isError(data) ? (
            <div className="w-full h-full justify-center items-center">{data.error}</div>
          ) : (
            <TestCaseTable view={RESULTS_PER_TOPO_TABLE} data={data} title="RESULTS PER TOPOLOGY" />
          )}
        </TabsContent>
        <TabsContent value="action_history" className="mt-4">
          {isError(actionHistoryData) ? (
            <div className="w-full h-full justify-center items-center">{actionHistoryData.error}</div>
          ) : (
            <TestCaseTable view={ACTION_HISTORY_TABLE} data={actionHistoryData} title="ACTIONHISTORY TABLE" />
          )}
        </TabsContent>
        <TabsContent value="run_history" className="mt-4">
          {isError(runHistoryData) ? (
            <div className="w-full h-full justify-center items-center">{runHistoryData.error}</div>
          ) : (
            <TestCaseTable view={RUN_HISTORY_TABLE} data={runHistoryData} title="RUNHISTORY TABLE" />
          )}
        </TabsContent>
        <TabsContent value="test_resources" className="mt-4">
          {isError(testResourcesData) ? (
            <div className="w-full h-full justify-center items-center">{testResourcesData.error}</div>
          ) : (
            <TestCaseTable view={TESTRESOURCES_TABLE} data={testResourcesData} title="TESTRESOURCES TABLE" />
          )}
        </TabsContent>
      </Tabs>
    </>
  );
}
