import { getSearchResults } from "@/api";
import { STANDARD_TEST_TABLE_VIEW } from "@/components/table/table_views";
import { TestCase, TestCaseTable } from "@/components/table/testcase-table";
import { Spinner } from "@/components/ui/spinner";
import { createFileRoute } from "@tanstack/react-router";
import { z } from "zod";

export const searchSchema = z.object({
  branch: z.string().optional(),
  regLevel: z.string().optional(),
  lastStatus: z.string().optional(),
  ownerTest: z.string().optional(),
  parentsuite: z.string().optional(),
  suite: z.string().optional(),
  action: z.string().optional(),
  testCase: z.string().optional(),
  cmd: z.string().optional(),
  branch_id: z.number().optional(),
  tasId: z.number().optional(),
  dumpPerTopo: z
    .number()
    .refine((val) => val === 0 || val === 1, {
      message: "dumpPerTopo must be 0 or 1",
    })
    .optional(),
});

export type SearchParams = z.infer<typeof searchSchema>;

export const Route = createFileRoute("/results")({
  component: RouteComponent,
  validateSearch: (search: Record<string, unknown>) => searchSchema.parse(search),
  loaderDeps: ({ search }) => search,
  loader: async ({ deps }) => {
    return await getSearchResults(deps);
  },

  pendingComponent: () => (
    <div className="w-full grow flex justify-center items-center">
      <Spinner size="large" className="text-primary" />
    </div>
  ),
});

function RouteComponent() {
  const testData = Route.useLoaderData();
  return <TestCaseTable view={STANDARD_TEST_TABLE_VIEW} data={testData as TestCase[]} title="DUMP PER TESTCASE" />;
}
