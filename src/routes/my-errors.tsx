import { createFileRoute } from "@tanstack/react-router";
import UserContext from "@/context/UserContext";
import { useContext, useState } from "react";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TestCaseTable } from "@/components/table/testcase-table";
import { SearchParams, searchSchema } from "./results";
import { getSearchResults } from "@/api";
import { Spinner } from "@/components/ui/spinner";
import { STANDARD_TEST_TABLE_VIEW } from "@/components/table/table_views";
import { isError } from "@/lib/utils";

export const Route = createFileRoute("/my-errors")({
  component: RouteComponent,
  pendingComponent: () => (
    <div className="w-full grow flex justify-center items-center">
      <Spinner size="large" className="text-primary" />
    </div>
  ),
  validateSearch: (search: Record<string, unknown>) => searchSchema.parse(search),
  loaderDeps: ({ search }) => search,
  loader: async ({ deps }) => {
    let search1: SearchParams = {
      lastStatus: "FAILED",
      action: "empty",
      ownerTest: deps.ownerTest,
    };
    let search2: SearchParams = {
      lastStatus: "FAILED",
      action: "!empty",
      ownerTest: deps.ownerTest,
    };

    return {
      empty_action_data: await getSearchResults(search1),
      non_empty_action_data: await getSearchResults(search2),
    };
  },
});

function RouteComponent() {
  const [_, setActiveTab] = useState("empty_action_fields");
  const userCtx = useContext(UserContext);

  const { empty_action_data, non_empty_action_data } = Route.useLoaderData();

  if (userCtx && userCtx.quickMyErrors) {
    return isError(empty_action_data) ? (
      <div className="w-full h-full justify-center items-center">{empty_action_data.error}</div>
    ) : (
      <TestCaseTable
        view={STANDARD_TEST_TABLE_VIEW}
        data={empty_action_data}
        title={"TESTS WITH EMPTY ACTION FIELDS"}
      />
    );
  }

  return (
    <Tabs defaultValue="empty_action_fields" className="mb-6" onValueChange={setActiveTab}>
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="empty_action_fields" className="cursor-pointer">
          TESTS WITH EMPTY ACTION FIELDS
        </TabsTrigger>
        <TabsTrigger value="non_empty_action_fields" className="cursor-pointer">
          TESTS WITH NON EMPTY ACTION FIELDS
        </TabsTrigger>
      </TabsList>
      <TabsContent value="empty_action_fields" className="mt-4">
        {isError(empty_action_data) ? (
          <div className="w-full h-full justify-center items-center">{empty_action_data.error}</div>
        ) : (
          <TestCaseTable
            view={STANDARD_TEST_TABLE_VIEW}
            data={empty_action_data}
            title="TESTS WITH EMPTY ACTION FIELDS"
          />
        )}
      </TabsContent>
      <TabsContent value="non_empty_action_fields" className="mt-4">
        {isError(non_empty_action_data) ? (
          <div className="w-full h-full justify-center items-center">{non_empty_action_data.error}</div>
        ) : (
          <TestCaseTable
            view={STANDARD_TEST_TABLE_VIEW}
            data={non_empty_action_data}
            title="TESTS WITH NON EMPTY ACTION FIELDS"
          />
        )}
      </TabsContent>
    </Tabs>
  );
}
