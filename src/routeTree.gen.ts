/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as TestDetailsRouteImport } from './routes/test-details'
import { Route as SearchMenuRouteImport } from './routes/search-menu'
import { Route as ResultsRouteImport } from './routes/results'
import { Route as ProfileRouteImport } from './routes/profile'
import { Route as MyErrorsRouteImport } from './routes/my-errors'
import { Route as IndexRouteImport } from './routes/index'

const TestDetailsRoute = TestDetailsRouteImport.update({
  id: '/test-details',
  path: '/test-details',
  getParentRoute: () => rootRouteImport,
} as any)
const SearchMenuRoute = SearchMenuRouteImport.update({
  id: '/search-menu',
  path: '/search-menu',
  getParentRoute: () => rootRouteImport,
} as any)
const ResultsRoute = ResultsRouteImport.update({
  id: '/results',
  path: '/results',
  getParentRoute: () => rootRouteImport,
} as any)
const ProfileRoute = ProfileRouteImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => rootRouteImport,
} as any)
const MyErrorsRoute = MyErrorsRouteImport.update({
  id: '/my-errors',
  path: '/my-errors',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/my-errors': typeof MyErrorsRoute
  '/profile': typeof ProfileRoute
  '/results': typeof ResultsRoute
  '/search-menu': typeof SearchMenuRoute
  '/test-details': typeof TestDetailsRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/my-errors': typeof MyErrorsRoute
  '/profile': typeof ProfileRoute
  '/results': typeof ResultsRoute
  '/search-menu': typeof SearchMenuRoute
  '/test-details': typeof TestDetailsRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/my-errors': typeof MyErrorsRoute
  '/profile': typeof ProfileRoute
  '/results': typeof ResultsRoute
  '/search-menu': typeof SearchMenuRoute
  '/test-details': typeof TestDetailsRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/my-errors'
    | '/profile'
    | '/results'
    | '/search-menu'
    | '/test-details'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/my-errors'
    | '/profile'
    | '/results'
    | '/search-menu'
    | '/test-details'
  id:
    | '__root__'
    | '/'
    | '/my-errors'
    | '/profile'
    | '/results'
    | '/search-menu'
    | '/test-details'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  MyErrorsRoute: typeof MyErrorsRoute
  ProfileRoute: typeof ProfileRoute
  ResultsRoute: typeof ResultsRoute
  SearchMenuRoute: typeof SearchMenuRoute
  TestDetailsRoute: typeof TestDetailsRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/test-details': {
      id: '/test-details'
      path: '/test-details'
      fullPath: '/test-details'
      preLoaderRoute: typeof TestDetailsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/search-menu': {
      id: '/search-menu'
      path: '/search-menu'
      fullPath: '/search-menu'
      preLoaderRoute: typeof SearchMenuRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/results': {
      id: '/results'
      path: '/results'
      fullPath: '/results'
      preLoaderRoute: typeof ResultsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/profile': {
      id: '/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof ProfileRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/my-errors': {
      id: '/my-errors'
      path: '/my-errors'
      fullPath: '/my-errors'
      preLoaderRoute: typeof MyErrorsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  MyErrorsRoute: MyErrorsRoute,
  ProfileRoute: ProfileRoute,
  ResultsRoute: ResultsRoute,
  SearchMenuRoute: SearchMenuRoute,
  TestDetailsRoute: TestDetailsRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
