import { UserProfile } from "@/api";
import { useProfileQuery } from "@/api/queries";
import { createContext, ReactNode } from "react";

interface ContextProviderProps {
  children: ReactNode;
}

const UserContext = createContext<UserProfile | undefined>(undefined);

export const UserProvider = ({ children }: ContextProviderProps) => {
  const { data } = useProfileQuery();

  return <UserContext.Provider value={data}>{children}</UserContext.Provider>;
};

export default UserContext;
